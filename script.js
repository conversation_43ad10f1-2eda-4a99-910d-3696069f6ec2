// Honda Banner Game States
const gameState = {
    collect: {
        slide: 0,
        collected: 0,
        items: []
    },
    scratch: {
        slide: 0,
        isDrawing: false,
        scratchPercent: 0,
        completed: false
    },
    memory: {
        slide: 0,
        cards: [],
        flippedCards: [],
        matches: 0,
        moves: 0,
        gameStarted: false
    }
};

// Honda Parts for Collect Game
const hondaParts = [
    { icon: '⚙️', name: 'Motor Yağı' },
    { icon: '🔧', name: 'Fren Balata' },
    { icon: '⚡', name: '<PERSON><PERSON><PERSON>' },
    { icon: '🛞', name: '<PERSON><PERSON>' },
    { icon: '🔩', name: '<PERSON><PERSON>i' },
    { icon: '🛡️', name: '<PERSON><PERSON><PERSON>' }
];

// Honda Models for Memory Game
const hondaModels = ['CBR1000RR', 'CB650R', 'Africa Twin', 'Gold Wing'];

// Sayfa yüklendiğinde çalışacak
document.addEventListener('DOMContentLoaded', function() {
    initCollectGame();
    initScratchGame();
    initMemoryGame();
});

// 1. COLLECT GAME - Honda Parça Toplama Oyunu
function initCollectGame() {
    const collectArea = document.getElementById('collect-area');
    if (!collectArea) return;

    collectArea.innerHTML = '';
    gameState.collect.collected = 0;
    gameState.collect.items = [];

    // 6 parça oluştur
    for (let i = 0; i < 6; i++) {
        const part = hondaParts[i];
        const item = document.createElement('div');
        item.className = 'collect-item';
        item.innerHTML = part.icon;
        item.title = part.name;
        item.onclick = () => collectItem(i, item);

        collectArea.appendChild(item);
        gameState.collect.items.push({ element: item, collected: false, part: part });
    }

    updateCollectDisplay();
}

function collectItem(index, element) {
    const item = gameState.collect.items[index];
    if (item.collected) return;

    // Parçayı topla
    item.collected = true;
    element.classList.add('collected');

    // İstatistikleri güncelle
    gameState.collect.collected++;

    updateCollectDisplay();

    // Tüm parçalar toplandığında sonuç ekranına geç
    if (gameState.collect.collected === 6) {
        setTimeout(() => {
            switchSlide('collect', 1);
        }, 1000);
    }
}

function updateCollectDisplay() {
    document.getElementById('collect-count').textContent = gameState.collect.collected + '/6';
}

// 2. SCRATCH GAME - Honda Kazı Kazan Oyunu
function initScratchGame() {
    const canvas = document.getElementById('scratch-canvas');
    if (!canvas) return;

    const ctx = canvas.getContext('2d');

    // Canvas boyutlarını ayarla
    canvas.width = 300;
    canvas.height = 120;

    // Başlangıçta gri overlay'i doldur
    ctx.globalCompositeOperation = 'source-over';
    ctx.fillStyle = '#888888';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Kazıma modunu ayarla
    ctx.globalCompositeOperation = 'destination-out';

    // Event listeners
    setupScratchEvents(canvas, ctx);

    // State sıfırla
    gameState.scratch.isDrawing = false;
    gameState.scratch.scratchPercent = 0;
    gameState.scratch.completed = false;

    updateScratchDisplay();
}

function setupScratchEvents(canvas, ctx) {
    const scratch = (x, y) => {
        ctx.beginPath();
        ctx.arc(x, y, 20, 0, 2 * Math.PI);
        ctx.fill();
    };

    const getPos = (e) => {
        const rect = canvas.getBoundingClientRect();
        let clientX, clientY;

        if (e.touches) {
            clientX = e.touches[0].clientX;
            clientY = e.touches[0].clientY;
        } else {
            clientX = e.clientX;
            clientY = e.clientY;
        }

        return {
            x: ((clientX - rect.left) / rect.width) * canvas.width,
            y: ((clientY - rect.top) / rect.height) * canvas.height
        };
    };

    const handleStart = (e) => {
        e.preventDefault();
        gameState.scratch.isDrawing = true;
        const { x, y } = getPos(e);
        scratch(x, y);
        checkScratchProgress(ctx);
    };

    const handleMove = (e) => {
        if (!gameState.scratch.isDrawing) return;
        e.preventDefault();
        const { x, y } = getPos(e);
        scratch(x, y);
        checkScratchProgress(ctx);
    };

    const handleEnd = (e) => {
        e.preventDefault();
        gameState.scratch.isDrawing = false;
    };

    // Mouse events
    canvas.addEventListener('mousedown', handleStart);
    canvas.addEventListener('mousemove', handleMove);
    canvas.addEventListener('mouseup', handleEnd);
    canvas.addEventListener('mouseleave', handleEnd);

    // Touch events
    canvas.addEventListener('touchstart', handleStart);
    canvas.addEventListener('touchmove', handleMove);
    canvas.addEventListener('touchend', handleEnd);
}

function checkScratchProgress(ctx) {
    const imageData = ctx.getImageData(0, 0, 300, 120);
    let clearedPixels = 0;

    for (let i = 0; i < imageData.data.length; i += 4) {
        if (imageData.data[i + 3] < 128) {
            clearedPixels++;
        }
    }

    const percent = Math.min((clearedPixels / (300 * 120)) * 100, 100);
    gameState.scratch.scratchPercent = Math.floor(percent);

    updateScratchDisplay();

    if (percent >= 60 && !gameState.scratch.completed) {
        gameState.scratch.completed = true;
        setTimeout(() => {
            switchSlide('scratch', 1);
        }, 1000);
    }
}

function updateScratchDisplay() {
    document.getElementById('scratch-percent').textContent = 'Kazıma: %' + gameState.scratch.scratchPercent;
}

// 3. MEMORY GAME - Honda Model Eşleştirme Oyunu
function initMemoryGame() {
    const grid = document.getElementById('memory-grid');
    if (!grid) return;

    grid.innerHTML = '';
    gameState.memory.cards = [];
    gameState.memory.flippedCards = [];
    gameState.memory.matches = 0;
    gameState.memory.moves = 0;
    gameState.memory.gameStarted = false;

    // 4 çift kart oluştur (8 kart)
    const cardPairs = [...hondaModels, ...hondaModels];
    const shuffledCards = shuffleArray(cardPairs);

    shuffledCards.forEach((model, index) => {
        const card = document.createElement('div');
        card.className = 'memory-card';
        card.innerHTML = '?';
        card.dataset.model = model;
        card.dataset.index = index;
        card.onclick = () => handleCardClick(index);

        grid.appendChild(card);
        gameState.memory.cards.push({
            element: card,
            model: model,
            flipped: false,
            matched: false
        });
    });

    updateMemoryDisplay();
}

function handleCardClick(index) {
    const card = gameState.memory.cards[index];

    if (card.flipped || card.matched || gameState.memory.flippedCards.length >= 2) {
        return;
    }

    if (!gameState.memory.gameStarted) {
        gameState.memory.gameStarted = true;
    }

    // Kartı çevir
    card.flipped = true;
    card.element.classList.add('flipped');
    card.element.innerHTML = card.model;
    gameState.memory.flippedCards.push(index);

    if (gameState.memory.flippedCards.length === 2) {
        gameState.memory.moves++;
        setTimeout(checkMemoryMatch, 1000);
    }

    updateMemoryDisplay();
}

function checkMemoryMatch() {
    const [first, second] = gameState.memory.flippedCards;
    const firstCard = gameState.memory.cards[first];
    const secondCard = gameState.memory.cards[second];

    if (firstCard.model === secondCard.model) {
        // Eşleşme var
        firstCard.matched = true;
        secondCard.matched = true;
        firstCard.element.classList.add('matched');
        secondCard.element.classList.add('matched');
        gameState.memory.matches++;

        if (gameState.memory.matches === 4) {
            setTimeout(() => {
                document.getElementById('final-moves').textContent = gameState.memory.moves;
                switchSlide('memory', 1);
            }, 500);
        }
    } else {
        // Eşleşme yok
        firstCard.flipped = false;
        secondCard.flipped = false;
        firstCard.element.classList.remove('flipped');
        secondCard.element.classList.remove('flipped');
        firstCard.element.innerHTML = '?';
        secondCard.element.innerHTML = '?';
    }

    gameState.memory.flippedCards = [];
    updateMemoryDisplay();
}

function updateMemoryDisplay() {
    document.getElementById('memory-matches').textContent = gameState.memory.matches + '/4';
    document.getElementById('memory-moves').textContent = gameState.memory.moves;
}

// UTILITY FUNCTIONS
function shuffleArray(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}

function switchSlide(bannerType, slideIndex) {
    const slide0 = document.getElementById(bannerType + '-slide-0');
    const slide1 = document.getElementById(bannerType + '-slide-1');
    const indicator0 = document.getElementById(bannerType + '-indicator-0');
    const indicator1 = document.getElementById(bannerType + '-indicator-1');

    if (slideIndex === 1) {
        slide0.classList.add('hidden');
        slide1.classList.remove('hidden');
        indicator0.classList.remove('active');
        indicator1.classList.add('active');
    } else {
        slide1.classList.add('hidden');
        slide0.classList.remove('hidden');
        indicator1.classList.remove('active');
        indicator0.classList.add('active');
    }

    gameState[bannerType].slide = slideIndex;
}

function goBack(bannerType) {
    switchSlide(bannerType, 0);

    // Oyunu sıfırla
    switch (bannerType) {
        case 'collect':
            initCollectGame();
            break;
        case 'scratch':
            initScratchGame();
            break;
        case 'memory':
            initMemoryGame();
            break;
    }
}











