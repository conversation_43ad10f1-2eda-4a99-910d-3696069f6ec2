// Honda Banner Game States
const gameState = {
    race: {
        slide: 0,
        isRunning: false,
        distance: 0,
        speed: 0,
        bikePosition: 50,
        obstacles: [],
        gameLoop: null
    },
    speed: {
        slide: 0,
        currentSpeed: 0,
        maxSpeed: 0,
        rpm: 0,
        isAccelerating: false
    },

    memory: {
        slide: 0,
        cards: [],
        flippedCards: [],
        matches: 0,
        moves: 0,
        gameStarted: false
    }
};

// Honda Models for Memory Game
const hondaModels = ['CBR1000RR', 'CB650R', 'Africa Twin', 'Gold Wing'];



// <PERSON>fa yüklendiğinde çalışacak
document.addEventListener('DOMContentLoaded', function() {
    initRaceGame();
    initSpeedGame();
    initMemoryGame();
});

// 1. RACE GAME - Honda Motor Yarış Oyunu
function initRaceGame() {
    const raceTrack = document.getElementById('race-track');
    const startBtn = document.getElementById('race-start');

    if (!raceTrack || !startBtn) return;

    // Reset game state
    gameState.race.isRunning = false;
    gameState.race.distance = 0;
    gameState.race.speed = 0;
    gameState.race.bikePosition = 50;
    gameState.race.obstacles = [];

    if (gameState.race.gameLoop) {
        clearInterval(gameState.race.gameLoop);
    }

    startBtn.onclick = startRace;
    updateRaceDisplay();
}

function startRace() {
    if (gameState.race.isRunning) return;

    gameState.race.isRunning = true;
    gameState.race.distance = 0;
    gameState.race.speed = 60;

    const startBtn = document.getElementById('race-start');
    startBtn.textContent = 'Yarış Devam Ediyor...';
    startBtn.disabled = true;

    // Keyboard controls
    document.addEventListener('keydown', handleRaceControls);
    document.addEventListener('keyup', handleRaceControls);

    // Game loop
    gameState.race.gameLoop = setInterval(updateRace, 100);

    // End race after 10 seconds
    setTimeout(endRace, 10000);
}

function handleRaceControls(e) {
    if (!gameState.race.isRunning) return;

    const bike = document.getElementById('honda-bike');
    if (!bike) return;

    if (e.type === 'keydown') {
        switch(e.key) {
            case 'ArrowUp':
                gameState.race.bikePosition = Math.max(20, gameState.race.bikePosition - 10);
                break;
            case 'ArrowDown':
                gameState.race.bikePosition = Math.min(80, gameState.race.bikePosition + 10);
                break;
            case ' ':
                e.preventDefault();
                gameState.race.speed = Math.min(200, gameState.race.speed + 10);
                break;
        }
        bike.style.top = gameState.race.bikePosition + '%';
    }
}

function updateRace() {
    gameState.race.distance += gameState.race.speed / 10;
    updateRaceDisplay();

    // Spawn obstacles randomly
    if (Math.random() < 0.3) {
        spawnObstacle();
    }
}

function spawnObstacle() {
    const track = document.getElementById('race-track');
    const obstacle = document.createElement('div');
    obstacle.className = 'obstacle';
    obstacle.style.top = Math.random() * 80 + 10 + '%';
    obstacle.style.right = '-30px';
    track.appendChild(obstacle);

    setTimeout(() => {
        if (obstacle.parentNode) {
            obstacle.parentNode.removeChild(obstacle);
        }
    }, 3000);
}

function endRace() {
    gameState.race.isRunning = false;

    if (gameState.race.gameLoop) {
        clearInterval(gameState.race.gameLoop);
    }

    document.removeEventListener('keydown', handleRaceControls);
    document.removeEventListener('keyup', handleRaceControls);

    document.getElementById('final-distance').textContent = Math.floor(gameState.race.distance);

    setTimeout(() => {
        switchSlide('race', 1);
    }, 1000);
}

function updateRaceDisplay() {
    document.getElementById('race-distance').textContent = Math.floor(gameState.race.distance) + 'm';
    document.getElementById('race-speed').textContent = Math.floor(gameState.race.speed) + ' km/h';
}

// 2. SPEED GAME - Honda Hız Testi Oyunu
function initSpeedGame() {
    const accelerateBtn = document.getElementById('accelerate-btn');
    if (!accelerateBtn) return;

    // Reset game state
    gameState.speed.currentSpeed = 0;
    gameState.speed.maxSpeed = 0;
    gameState.speed.rpm = 0;
    gameState.speed.isAccelerating = false;

    accelerateBtn.onclick = accelerate;
    updateSpeedDisplay();
    updateSpeedometer();
}

function accelerate() {
    if (gameState.speed.isAccelerating) return;

    gameState.speed.isAccelerating = true;
    const accelerateBtn = document.getElementById('accelerate-btn');
    accelerateBtn.textContent = 'Hızlanıyor...';

    // Accelerate for 3 seconds
    const accelerateInterval = setInterval(() => {
        gameState.speed.currentSpeed += Math.random() * 20 + 10;
        gameState.speed.rpm += Math.random() * 500 + 200;

        if (gameState.speed.currentSpeed > gameState.speed.maxSpeed) {
            gameState.speed.maxSpeed = gameState.speed.currentSpeed;
        }

        // Limit max values
        gameState.speed.currentSpeed = Math.min(300, gameState.speed.currentSpeed);
        gameState.speed.rpm = Math.min(12000, gameState.speed.rpm);

        updateSpeedDisplay();
        updateSpeedometer();
    }, 100);

    setTimeout(() => {
        clearInterval(accelerateInterval);

        // Decelerate
        const decelerateInterval = setInterval(() => {
            gameState.speed.currentSpeed *= 0.9;
            gameState.speed.rpm *= 0.9;

            updateSpeedDisplay();
            updateSpeedometer();

            if (gameState.speed.currentSpeed < 5) {
                clearInterval(decelerateInterval);
                gameState.speed.isAccelerating = false;
                accelerateBtn.textContent = 'Tekrar Hızlan!';

                // Check if max speed reached for completion
                if (gameState.speed.maxSpeed > 200) {
                    setTimeout(() => {
                        document.getElementById('final-max-speed').textContent = Math.floor(gameState.speed.maxSpeed);
                        switchSlide('speed', 1);
                    }, 1000);
                }
            }
        }, 100);
    }, 3000);
}

function updateSpeedDisplay() {
    document.getElementById('current-speed').textContent = Math.floor(gameState.speed.currentSpeed);
    document.getElementById('current-rpm').textContent = Math.floor(gameState.speed.rpm);
    document.getElementById('max-speed').textContent = Math.floor(gameState.speed.maxSpeed) + ' km/h';
}

function updateSpeedometer() {
    const needle = document.getElementById('speed-needle');
    if (!needle) return;

    // Convert speed to angle (0-300 km/h = -90 to 90 degrees)
    const angle = -90 + (gameState.speed.currentSpeed / 300) * 180;
    needle.style.transform = `rotate(${angle}deg)`;
}



// 3. MEMORY GAME - Honda Model Eşleştirme Oyunu
function initMemoryGame() {
    const grid = document.getElementById('memory-grid');
    if (!grid) return;

    grid.innerHTML = '';
    gameState.memory.cards = [];
    gameState.memory.flippedCards = [];
    gameState.memory.matches = 0;
    gameState.memory.moves = 0;
    gameState.memory.gameStarted = false;

    // 4 çift kart oluştur (8 kart)
    const cardPairs = [...hondaModels, ...hondaModels];
    const shuffledCards = shuffleArray(cardPairs);

    shuffledCards.forEach((model, index) => {
        const card = document.createElement('div');
        card.className = 'memory-card';
        card.innerHTML = '?';
        card.dataset.model = model;
        card.dataset.index = index;
        card.onclick = () => handleCardClick(index);

        grid.appendChild(card);
        gameState.memory.cards.push({
            element: card,
            model: model,
            flipped: false,
            matched: false
        });
    });

    updateMemoryDisplay();
}

function handleCardClick(index) {
    const card = gameState.memory.cards[index];

    if (card.flipped || card.matched || gameState.memory.flippedCards.length >= 2) {
        return;
    }

    if (!gameState.memory.gameStarted) {
        gameState.memory.gameStarted = true;
    }

    // Kartı çevir
    card.flipped = true;
    card.element.classList.add('flipped');
    card.element.innerHTML = card.model;
    gameState.memory.flippedCards.push(index);

    if (gameState.memory.flippedCards.length === 2) {
        gameState.memory.moves++;
        setTimeout(checkMemoryMatch, 1000);
    }

    updateMemoryDisplay();
}

function checkMemoryMatch() {
    const [first, second] = gameState.memory.flippedCards;
    const firstCard = gameState.memory.cards[first];
    const secondCard = gameState.memory.cards[second];

    if (firstCard.model === secondCard.model) {
        // Eşleşme var
        firstCard.matched = true;
        secondCard.matched = true;
        firstCard.element.classList.add('matched');
        secondCard.element.classList.add('matched');
        gameState.memory.matches++;

        if (gameState.memory.matches === 4) {
            setTimeout(() => {
                document.getElementById('final-moves').textContent = gameState.memory.moves;
                switchSlide('memory', 1);
            }, 500);
        }
    } else {
        // Eşleşme yok
        firstCard.flipped = false;
        secondCard.flipped = false;
        firstCard.element.classList.remove('flipped');
        secondCard.element.classList.remove('flipped');
        firstCard.element.innerHTML = '?';
        secondCard.element.innerHTML = '?';
    }

    gameState.memory.flippedCards = [];
    updateMemoryDisplay();
}

function updateMemoryDisplay() {
    document.getElementById('memory-matches').textContent = gameState.memory.matches + '/4';
    document.getElementById('memory-moves').textContent = gameState.memory.moves;
}

// UTILITY FUNCTIONS
function shuffleArray(array) {
    const newArray = [...array];
    for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}

function switchSlide(bannerType, slideIndex) {
    const slide0 = document.getElementById(bannerType + '-slide-0');
    const slide1 = document.getElementById(bannerType + '-slide-1');
    const indicator0 = document.getElementById(bannerType + '-indicator-0');
    const indicator1 = document.getElementById(bannerType + '-indicator-1');

    if (slideIndex === 1) {
        slide0.classList.add('hidden');
        slide1.classList.remove('hidden');
        indicator0.classList.remove('active');
        indicator1.classList.add('active');
    } else {
        slide1.classList.add('hidden');
        slide0.classList.remove('hidden');
        indicator1.classList.remove('active');
        indicator0.classList.add('active');
    }

    gameState[bannerType].slide = slideIndex;
}

function goBack(bannerType) {
    switchSlide(bannerType, 0);

    // Oyunu sıfırla
    switch (bannerType) {
        case 'race':
            initRaceGame();
            break;
        case 'speed':
            initSpeedGame();
            break;

        case 'memory':
            initMemoryGame();
            break;
    }
}











