* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    padding: 20px;
}

/* Honda Banner Ana Container */
.honda-banner {
    width: 970px;
    height: 250px;
    background: linear-gradient(135deg, #DC143C 0%, #B91C3C 50%, #000000 100%);
    border-radius: 15px;
    position: relative;
    overflow: hidden;
    box-shadow: 0 8px 25px rgba(220, 20, 60, 0.3);
    margin: 0 auto 30px auto;
    border: 2px solid #DC143C;
}

/* Honda Logo */
.honda-logo {
    background: #DC143C;
    color: white;
    padding: 8px 16px;
    border-radius: 8px;
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 15px;
    display: inline-block;
    border: 2px solid #FFD700;
}

/* Slide Content */
.slide-content {
    display: flex;
    align-items: center;
    height: 100%;
    padding: 25px;
    position: absolute;
    width: 100%;
    top: 0;
    left: 0;
    opacity: 1;
    transform: translateX(0);
    transition: all 0.5s ease-in-out;
}

.slide-content.hidden {
    opacity: 0;
    transform: translateX(100%);
    pointer-events: none;
}

.slide-content.slide-in {
    animation: slideIn 0.5s ease-in-out;
}

.slide-content.slide-out {
    animation: slideOut 0.5s ease-in-out;
}

@keyframes slideIn {
    from {
        opacity: 0;
        transform: translateX(120px);
    }
    to {
        opacity: 1;
        transform: translateX(0);
    }
}

@keyframes slideOut {
    from {
        opacity: 1;
        transform: translateX(0);
    }
    to {
        opacity: 0;
        transform: translateX(-120px);
    }
}

/* Banner Left */
.banner-left {
    flex: 1;
    position: relative;
    display: flex;
    flex-direction: column;
    justify-content: center;
}

.banner-left h2 {
    color: white;
    font-size: 22px;
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.5);
}

.highlight {
    color: #FFD700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
}

.back-btn {
    background: #DC143C;
    color: white;
    border: 2px solid #FFD700;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    margin-bottom: 15px;
    transition: all 0.2s ease;
    font-size: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.back-btn:hover {
    transform: scale(1.1);
    background: #FFD700;
    color: #DC143C;
}

/* Banner Right - Game Area */
.banner-right {
    flex: 1.2;
    position: relative;
}

.game-area {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.game-container {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 20px;
    width: 100%;
    max-width: 400px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.game-message {
    color: white;
    font-size: 14px;
    text-align: center;
    margin-bottom: 15px;
    font-weight: 600;
}

.start-btn {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #000;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
    width: 100%;
    margin: 10px 0;
}

.start-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

.game-stats {
    display: flex;
    justify-content: space-between;
    color: white;
    font-size: 12px;
    margin-top: 10px;
}

.game-stats span {
    background: rgba(0, 0, 0, 0.3);
    padding: 5px 10px;
    border-radius: 15px;
}

/* Collect Game Styles */
.collect-area {
    width: 100%;
    height: 120px;
    background: rgba(0, 0, 0, 0.2);
    border-radius: 8px;
    position: relative;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    justify-content: center;
    gap: 10px;
    padding: 10px;
    border: 2px solid #FFD700;
}

.collect-item {
    width: 40px;
    height: 40px;
    background: linear-gradient(45deg, #FFD700, #FFA500);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid #DC143C;
    animation: bounce 2s infinite;
}

.collect-item:hover {
    transform: scale(1.2);
    box-shadow: 0 0 20px #FFD700;
}

.collect-item.collected {
    animation: collectAnim 0.5s ease forwards;
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

@keyframes collectAnim {
    0% { transform: scale(1); opacity: 1; }
    50% { transform: scale(1.5); opacity: 0.7; }
    100% { transform: scale(0); opacity: 0; }
}

.discount-display {
    color: #FFD700;
    font-size: 18px;
    font-weight: bold;
    text-align: center;
    margin-bottom: 15px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
}

/* Scratch Game Styles */
.scratch-container {
    position: relative;
    width: 300px;
    height: 120px;
    margin: 15px auto;
    border-radius: 12px;
    overflow: hidden;
    border: 3px solid #FFD700;
}

.scratch-reward {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    width: 100%;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 24px;
    font-weight: bold;
    color: #DC143C;
    border-radius: 8px;
}

#scratch-canvas {
    position: absolute;
    top: 0;
    left: 0;
    cursor: pointer;
    border-radius: 8px;
}

.scratch-progress {
    color: white;
    text-align: center;
    margin-top: 10px;
    font-size: 14px;
}

/* Memory Game Styles */
.memory-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 10px;
    margin: 15px 0;
    max-width: 320px;
    margin-left: auto;
    margin-right: auto;
}

.memory-card {
    width: 70px;
    height: 70px;
    background: linear-gradient(45deg, #DC143C, #B91C3C);
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    font-size: 12px;
    color: white;
    font-weight: bold;
    transition: all 0.3s ease;
    border: 2px solid #FFD700;
    text-align: center;
    line-height: 1.2;
}

.memory-card:hover {
    transform: scale(1.05);
    box-shadow: 0 0 15px #FFD700;
}

.memory-card.flipped {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #DC143C;
}

.memory-card.matched {
    background: linear-gradient(45deg, #00C851, #007E33);
    color: white;
    animation: matchPulse 0.5s ease;
}

@keyframes matchPulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.1); }
    100% { transform: scale(1); }
}

/* Info Content Styles */
.info-content {
    color: white;
    padding: 20px;
    text-align: center;
}

.info-content h3 {
    font-size: 20px;
    margin-bottom: 20px;
    color: #FFD700;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
}

.discount-result {
    margin: 20px 0;
}

.big-discount {
    font-size: 48px;
    font-weight: bold;
    color: #FFD700;
    text-shadow: 3px 3px 6px rgba(0,0,0,0.8);
    margin-bottom: 10px;
}

.reward-info, .model-list {
    margin-bottom: 20px;
}

.reward-item, .model-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 10px 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.reward-icon, .part-icon {
    font-size: 20px;
    margin-right: 10px;
}

.reward-text, .model-name {
    font-weight: 600;
}

.model-type {
    color: #FFD700;
    font-size: 12px;
}

.cta-btn {
    display: block;
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #DC143C;
    text-decoration: none;
    padding: 15px 30px;
    border-radius: 25px;
    text-align: center;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
    margin-top: 20px;
}

.cta-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
    background: linear-gradient(45deg, #FFA500, #FFD700);
}

/* Banner Indicators */
.banner-indicators {
    position: absolute;
    bottom: 15px;
    left: 50%;
    transform: translateX(-50%);
    display: flex;
    gap: 8px;
}

.banner-indicators span {
    width: 10px;
    height: 10px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    cursor: pointer;
    transition: all 0.3s ease;
}

.banner-indicators span.active {
    background: #FFD700;
    transform: scale(1.2);
}
/* Responsive Design */
@media (max-width: 1024px) {
    .honda-banner {
        width: 100%;
        max-width: 970px;
    }
}

@media (max-width: 768px) {
    .honda-banner {
        height: 300px;
    }

    .slide-content {
        flex-direction: column;
        padding: 15px;
    }

    .banner-left, .banner-right {
        flex: 1;
        width: 100%;
    }

    .banner-left h2 {
        font-size: 18px;
        text-align: center;
        margin-bottom: 10px;
    }

    .game-container {
        max-width: 100%;
        padding: 15px;
    }
}



/* Race Game Styles */
.race-track {
    width: 100%;
    height: 120px;
    background: linear-gradient(to bottom, #333 0%, #555 50%, #333 100%);
    border-radius: 8px;
    position: relative;
    overflow: hidden;
    border: 2px solid #FFD700;
}

.honda-bike {
    position: absolute;
    left: 20px;
    top: 50%;
    transform: translateY(-50%);
    font-size: 30px;
    transition: all 0.3s ease;
    z-index: 10;
}

.obstacles {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.obstacle {
    position: absolute;
    width: 30px;
    height: 30px;
    background: #DC143C;
    border-radius: 4px;
    animation: moveObstacle 3s linear infinite;
}

.road-lines {
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
}

.road-line {
    position: absolute;
    width: 40px;
    height: 4px;
    background: #FFD700;
    top: 50%;
    transform: translateY(-50%);
    animation: moveLine 2s linear infinite;
}

.road-line:nth-child(1) { animation-delay: 0s; }
.road-line:nth-child(2) { animation-delay: -0.7s; }
.road-line:nth-child(3) { animation-delay: -1.4s; }

@keyframes moveObstacle {
    from { right: -30px; }
    to { right: 100%; }
}

@keyframes moveLine {
    from { right: -40px; }
    to { right: 100%; }
}

.control-btn {
    background: linear-gradient(45deg, #FFD700, #FFA500);
    color: #DC143C;
    border: none;
    padding: 12px 24px;
    border-radius: 25px;
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 215, 0, 0.3);
    width: 100%;
    margin: 10px 0;
}

.control-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 215, 0, 0.4);
}

.control-btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 10px rgba(255, 215, 0, 0.3);
}

/* Speed Game Styles */
.speed-game-area {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    margin: 15px 0;
}

.honda-cbr-clickable {
    flex: 1;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 15px;
    border: 2px solid #FFD700;
}

.honda-cbr-clickable:hover {
    transform: scale(1.05);
    box-shadow: 0 0 20px rgba(255, 215, 0, 0.5);
}

.honda-cbr-clickable:active {
    transform: scale(0.95);
    filter: brightness(1.2);
}

.honda-cbr-clickable img {
    width: 100px;
    height: auto;
    border-radius: 8px;
    transition: all 0.3s ease;
    display: block;
    margin: 0 auto;
}

.click-text {
    color: #FFD700;
    font-size: 14px;
    font-weight: bold;
    margin-top: 10px;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.7);
}

.speedometer {
    width: 150px;
    height: 150px;
    border: 6px solid #FFD700;
    border-radius: 50%;
    position: relative;
    background: radial-gradient(circle, #000 0%, #333 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
}

.speed-needle {
    position: absolute;
    width: 3px;
    height: 60px;
    background: #DC143C;
    border-radius: 2px;
    transform-origin: bottom center;
    transform: rotate(-90deg);
    transition: transform 0.5s ease;
    z-index: 5;
}

.speed-needle::after {
    content: '';
    position: absolute;
    bottom: -8px;
    left: 50%;
    transform: translateX(-50%);
    width: 12px;
    height: 12px;
    background: #DC143C;
    border-radius: 50%;
}

.speed-display {
    text-align: center;
    color: #FFD700;
    font-size: 20px;
    font-weight: bold;
    z-index: 10;
}

.speed-display small {
    font-size: 10px;
    display: block;
    margin-top: 3px;
}

.speed-controls {
    text-align: center;
    margin-top: 15px;
}



/* Utility Classes */
.hidden {
    display: none !important;
}